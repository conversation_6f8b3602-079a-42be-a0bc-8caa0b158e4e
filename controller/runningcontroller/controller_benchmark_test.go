package runningcontroller

import (
	"fmt"
	"math/rand"
	"sync"
	"testing"
	"time"

	"git.moderntv.eu/mcloud/system/common"
	"git.moderntv.eu/mcloud/system/controller"
)

// BenchmarkControllerTaskProcessing benchmarks the controller's ability to handle
// large numbers of tasks and process changes efficiently
func BenchmarkControllerTaskProcessing(b *testing.B) {
	taskCounts := []int{1000, 3000, 5000, 10000}
	changeCount := 5000

	for _, taskCount := range taskCounts {
		b.Run(fmt.Sprintf("Tasks_%d_Changes_%d", taskCount, changeCount), func(b *testing.B) {
			benchmarkTaskProcessing(b, taskCount, changeCount)
		})
	}
}

func benchmarkTaskProcessing(b *testing.B, taskCount int, changeCount int) {
	// Setup controller - use a dummy testing.T for GetController
	dummyT := &testing.T{}
	controllerTest := GetController(dummyT)
	defer controllerTest.Close()

	ctrl := controllerTest.Controller
	locks := controller.NewLocks()

	// Create a node to run tasks on
	dbNode := &common.DbNode{
		Id:    "benchmark-node",
		Group: "benchmark",
		State: "running",
		FreeResources: common.MultiResources{
			"CPU": {1000.0},
			"GPU": {100.0},
		},
	}
	ctrl.cloud.AddNode(dbNode, locks)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// Phase 1: Add initial tasks
		b.StartTimer()
		tasks := make([]*common.DbTask, taskCount)

		addStart := time.Now()
		for j := 0; j < taskCount; j++ {
			task := &common.DbTask{
				DbId:   j + 1,
				Id:     fmt.Sprintf("benchmark-task-%d-%d", i, j),
				Action: "run",
				RequiredResources: &common.Resources{
					"CPU": float64(rand.Intn(10) + 1), // 1-10 CPU
					"GPU": float64(rand.Intn(5)),      // 0-4 GPU
				},
				Requirements: &common.Flags{
					"finite": true,
				},
			}
			tasks[j] = task
			ctrl.AddTask(task)
		}
		addDuration := time.Since(addStart)

		// Phase 2: Process changes (simulate task state updates)
		changeStart := time.Now()

		// Simulate concurrent task state changes
		var wg sync.WaitGroup
		changeChan := make(chan int, changeCount)

		// Fill change queue
		for j := 0; j < changeCount; j++ {
			changeChan <- j
		}
		close(changeChan)

		// Process changes concurrently
		workerCount := 10
		for w := 0; w < workerCount; w++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for changeIdx := range changeChan {
					taskIdx := changeIdx % taskCount
					task := tasks[taskIdx]

					// Simulate different types of changes
					changeType := changeIdx % 4
					switch changeType {
					case 0: // Task state update to running
						nodeTask := &common.DbNodeTask{
							Id:               fmt.Sprintf("nt-%s", task.Id),
							Task:             task.DbId,
							Node:             "benchmark-node",
							State:            "running",
							LastOnlineTime:   &time.Time{},
							LastRunningTime:  &time.Time{},
							RunningSinceTime: &time.Time{},
							OriginTask: common.RunnableTask{
								DbTask:            *task,
								SelectedResources: common.NewSelectedResources(),
							},
							ResourceUsage: common.MultiResources{
								"CPU": {float64(rand.Intn(5) + 1)},
								"GPU": {float64(rand.Intn(2))},
							},
						}
						locks := controller.NewLocks()
						ctrl.cloud.AddNodeTask(nodeTask, locks)

					case 1: // Task completion
						nodeTask := &common.DbNodeTask{
							Id:               fmt.Sprintf("nt-%s", task.Id),
							Task:             task.DbId,
							Node:             "benchmark-node",
							State:            "done",
							LastOnlineTime:   &time.Time{},
							LastRunningTime:  &time.Time{},
							RunningSinceTime: &time.Time{},
							OriginTask: common.RunnableTask{
								DbTask:            *task,
								SelectedResources: common.NewSelectedResources(),
							},
							ResourceUsage: common.MultiResources{
								"CPU": {float64(rand.Intn(5) + 1)},
								"GPU": {float64(rand.Intn(2))},
							},
						}
						locks := controller.NewLocks()
						ctrl.cloud.AddNodeTask(nodeTask, locks)

					case 2: // Resource usage update
						nodeTask := &common.DbNodeTask{
							Id:               fmt.Sprintf("nt-%s", task.Id),
							Task:             task.DbId,
							Node:             "benchmark-node",
							State:            "running",
							LastOnlineTime:   &time.Time{},
							LastRunningTime:  &time.Time{},
							RunningSinceTime: &time.Time{},
							OriginTask: common.RunnableTask{
								DbTask:            *task,
								SelectedResources: common.NewSelectedResources(),
							},
							ResourceUsage: common.MultiResources{
								"CPU": {float64(rand.Intn(8) + 1)}, // Updated usage
								"GPU": {float64(rand.Intn(3))},     // Updated usage
							},
						}
						locks := controller.NewLocks()
						ctrl.cloud.AddNodeTask(nodeTask, locks)

					case 3: // Task removal
						locks := controller.NewLocks()
						ctrl.RemoveTask(task, locks)
					}
				}
			}()
		}

		wg.Wait()
		changeDuration := time.Since(changeStart)

		// Phase 3: Cleanup remaining tasks
		cleanupStart := time.Now()
		for _, task := range tasks {
			locks := controller.NewLocks()
			ctrl.RemoveTask(task, locks)
		}
		cleanupDuration := time.Since(cleanupStart)

		b.StopTimer()

		// Report detailed timing
		b.ReportMetric(float64(addDuration.Nanoseconds())/1e6, "add_ms")
		b.ReportMetric(float64(changeDuration.Nanoseconds())/1e6, "changes_ms")
		b.ReportMetric(float64(cleanupDuration.Nanoseconds())/1e6, "cleanup_ms")
		b.ReportMetric(float64(changeCount)/changeDuration.Seconds(), "changes_per_sec")
		b.ReportMetric(float64(taskCount)/addDuration.Seconds(), "tasks_added_per_sec")
	}
}

// BenchmarkControllerConcurrentAccess benchmarks concurrent access to controller
func BenchmarkControllerConcurrentAccess(b *testing.B) {
	dummyT := &testing.T{}
	controllerTest := GetController(dummyT)
	defer controllerTest.Close()

	ctrl := controllerTest.Controller
	taskCount := 1000
	concurrency := 50

	// Create a node
	locks := controller.NewLocks()
	dbNode := &common.DbNode{
		Id:    "concurrent-node",
		Group: "benchmark",
		State: "running",
		FreeResources: common.MultiResources{
			"CPU": {1000.0},
			"GPU": {100.0},
		},
	}
	ctrl.cloud.AddNode(dbNode, locks)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		var wg sync.WaitGroup

		// Concurrent task operations
		for c := 0; c < concurrency; c++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()

				// Each worker handles a subset of tasks
				tasksPerWorker := taskCount / concurrency
				startIdx := workerID * tasksPerWorker
				endIdx := startIdx + tasksPerWorker

				for j := startIdx; j < endIdx; j++ {
					task := &common.DbTask{
						DbId:   j + 1,
						Id:     fmt.Sprintf("concurrent-task-%d-%d", i, j),
						Action: "run",
						RequiredResources: &common.Resources{
							"CPU": float64(rand.Intn(10) + 1),
							"GPU": float64(rand.Intn(5)),
						},
						Requirements: &common.Flags{
							"finite": true,
						},
					}

					// Add task
					ctrl.AddTask(task)

					// Simulate some processing
					time.Sleep(time.Microsecond * 10)

					// Remove task
					locks := controller.NewLocks()
					ctrl.RemoveTask(task, locks)
				}
			}(c)
		}

		wg.Wait()
	}
}

// BenchmarkControllerMemoryUsage benchmarks memory usage with many tasks
func BenchmarkControllerMemoryUsage(b *testing.B) {
	taskCounts := []int{1000, 5000, 10000}

	for _, taskCount := range taskCounts {
		b.Run(fmt.Sprintf("Tasks_%d", taskCount), func(b *testing.B) {
			dummyT := &testing.T{}
			controllerTest := GetController(dummyT)
			defer controllerTest.Close()

			ctrl := controllerTest.Controller
			locks := controller.NewLocks()

			// Create a node
			dbNode := &common.DbNode{
				Id:    "memory-node",
				Group: "benchmark",
				State: "running",
				FreeResources: common.MultiResources{
					"CPU": {10000.0},
					"GPU": {1000.0},
				},
			}
			ctrl.cloud.AddNode(dbNode, locks)

			b.ResetTimer()

			for i := 0; i < b.N; i++ {
				tasks := make([]*common.DbTask, taskCount)

				// Add all tasks
				for j := 0; j < taskCount; j++ {
					task := &common.DbTask{
						DbId:   j + 1,
						Id:     fmt.Sprintf("memory-task-%d-%d", i, j),
						Action: "run",
						RequiredResources: &common.Resources{
							"CPU": float64(rand.Intn(10) + 1),
							"GPU": float64(rand.Intn(5)),
						},
						Requirements: &common.Flags{
							"finite": true,
						},
					}
					tasks[j] = task
					ctrl.AddTask(task)
				}

				// Keep tasks in memory for a while to measure peak usage
				time.Sleep(time.Millisecond * 100)

				// Clean up
				for _, task := range tasks {
					locks := controller.NewLocks()
					ctrl.RemoveTask(task, locks)
				}
			}
		})
	}
}
