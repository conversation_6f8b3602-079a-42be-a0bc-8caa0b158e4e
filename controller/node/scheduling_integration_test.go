package node

import (
	"testing"
	"time"

	"github.com/matryer/is"

	"git.moderntv.eu/mcloud/system/common"
)

// TestSchedulingResourceIntegration tests the complete flow of how scheduling
// interacts with resource management, including reservations and potential reserves
func TestSchedulingResourceIntegration(t *testing.T) {
	t.<PERSON>llel()
	is := is.New(t)

	var now = time.Now()
	var startTime = now.Add(-10 * time.Minute)
	var oldTime = now.Add(-30 * time.Second) // Old enough to not be reserved

	t.Run("complete scheduling flow", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		// Initialize node resources
		nr := NewResources()
		nr.UpdateResources(map[string][]float64{
			"GPU": {1000.0, 2000.0, 1500.0},
			"CPU": {4000.0, 8000.0},
		}, nil)

		// Verify initial state
		gpuAvail, _ := nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 2000.0, 1500.0})

		cpuAvail, _ := nr.GetAvailable("CPU")
		is.Equal(cpuAvail, []float64{4000.0, 8000.0})

		// Scenario 1: Add a starting task (should create reservation)
		dbTask1 := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 500.0, "CPU": 2000.0},
		}

		sr1 := common.NewSelectedResources()
		sr1["GPU"] = 1 // Use GPU index 1
		sr1["CPU"] = 0 // Use CPU index 0

		dbNodeTask1 := common.DbNodeTask{
			Id:             "nodetask1",
			Task:           dbTask1.DbId,
			Node:           "node1",
			State:          "starting",
			StartTime:      &startTime,
			LastOnlineTime: &now,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr1,
			},
			ResourceUsage: common.MultiResources{
				"GPU": {0.0, 100.0, 0.0}, // Using 100 out of 500 required
				"CPU": {500.0, 0.0},      // Using 500 out of 2000 required
			},
		}

		nr.AddTask(&dbNodeTask1)

		// Check available resources after adding starting task
		// FIXED: Starting task reserves full required amount (500) for GPU, (2000) for CPU
		// GPU: [1000, 2000-500, 1500] = [1000, 1500, 1500] (previously was 2000-400=1600)
		// CPU: [4000-2000, 8000] = [2000, 8000] (previously was 4000-1500=2500)
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 1500.0, 1500.0})

		cpuAvail, _ = nr.GetAvailable("CPU")
		is.Equal(cpuAvail, []float64{2000.0, 8000.0})

		// Scenario 2: Add a running task (should create potential reserve)
		dbTask2 := common.DbTask{
			DbId:              2,
			Id:                "task2",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 800.0},
		}

		sr2 := common.NewSelectedResources()
		sr2["GPU"] = 2 // Use GPU index 2

		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &oldTime, // Old enough to not be reserved
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr2,
			},
			ResourceUsage: common.MultiResources{
				"GPU": {0.0, 0.0, 200.0}, // Using 200 out of 800 required
			},
		}

		nr.AddTask(&dbNodeTask2)

		// Check available resources after adding running task
		// GPU: [1000, 1500, 1500-600] = [1000, 1500, 900] (max reserve 600 = 800-200)
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 1500.0, 900.0})

		// Scenario 3: Add another running task with higher potential reserve
		dbTask3 := common.DbTask{
			DbId:              3,
			Id:                "task3",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 1200.0},
		}

		sr3 := common.NewSelectedResources()
		sr3["GPU"] = 2 // Use same GPU index as task2

		dbNodeTask3 := common.DbNodeTask{
			Id:               "nodetask3",
			Task:             dbTask3.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &oldTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask3,
				SelectedResources: sr3,
			},
			ResourceUsage: common.MultiResources{
				"GPU": {0.0, 0.0, 100.0}, // Using 100 out of 1200 required
			},
		}

		nr.AddTask(&dbNodeTask3)

		// Check available resources - should use max potential reserve
		// Current logic: GPU: [1000, 1500, 1500-1100] = [1000, 1500, 400] (max reserve 1100 = max(600, 1100))
		// New logic: GPU: [1000, 1500, 1500-1100] = [1000, 1500, 400] (largest difference from Task3 = 1100)
		// In this case, both give same result
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 1500.0, 400.0})

		// Scenario 4: Task1 transitions from starting to running
		dbNodeTask1.State = "running"
		dbNodeTask1.LastRunningTime = &now
		dbNodeTask1.RunningSinceTime = &oldTime

		nr.AddTask(&dbNodeTask1) // This should remove old and add new

		// Now task1 should create potential reserve instead of immediate reservation
		// FIXED: Now task1 is old running task, so reserves (required - usage)
		// GPU: [1000, 2000-(500-100), 1500-1100] = [1000, 1600, 400] (task1 reserve 400 = 500-100)
		// CPU: [4000-(2000-500), 8000] = [2500, 8000] (task1 reserve 1500 = 2000-500)
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 2000.0, 400.0}) // Updated for single task logic

		cpuAvail, _ = nr.GetAvailable("CPU")
		is.Equal(cpuAvail, []float64{2500.0, 8000.0})

		// Scenario 5: Remove task with highest potential reserve
		nr.RemoveTask(&dbNodeTask3)

		// Current logic: GPU: [1000, 1600, 1500-600] = [1000, 1600, 900] (now max reserve is 600 from task2)
		// New logic: GPU: [1000, 2000, 1500-600] = [1000, 2000, 900] (largest difference is 600 from task2)
		// Updated for single task logic
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 2000.0, 900.0})

		// Scenario 6: Remove all tasks and verify cleanup
		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)

		// Should return to original state
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 2000.0, 1500.0})

		cpuAvail, _ = nr.GetAvailable("CPU")
		is.Equal(cpuAvail, []float64{4000.0, 8000.0})
	})

	t.Run("resource dimension changes during scheduling", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0, 2000.0}}, nil)

		// Add a task
		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 500.0},
		}

		sr := common.NewSelectedResources()
		sr["GPU"] = 1

		dbNodeTask := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": {0.0, 100.0}},
		}

		nr.AddTask(&dbNodeTask)

		// Verify initial state
		gpuAvail, _ := nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 1500.0})

		// Change resource dimensions
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0, 2000.0, 3000.0}}, nil)

		// Task should still be properly handled
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 1500.0, 3000.0})

		// Remove task
		nr.RemoveTask(&dbNodeTask)

		// Should return to new dimensions
		gpuAvail, _ = nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 2000.0, 3000.0})
	})

	t.Run("edge case: nil slice handling in recalculation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0}}, nil)

		// Manually create a scenario that could trigger the nil slice issue
		// This tests the fix for the unnecessary nil check
		prr := nr.peakRunningRequirements

		// Add task reserves directly to create mixed state
		prr.AddTaskReserve(1, "GPU", []float64{100.0})
		prr.taskReserves[2] = make(map[string][]float64) // Task 2 exists but has no GPU reserves

		// This should not panic and should work correctly
		prr.RecalculateMaxReserveForResource("GPU")

		// Verify the result is correct
		maxReserve := prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 100.0)

		// Verify available calculation works
		gpuAvail, _ := nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{900.0}) // 1000 - 100 = 900
	})
}
