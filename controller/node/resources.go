package node

import (
	"git.moderntv.eu/mcloud/system/common"
)

// Resources is the main coordinator that maintains API compatibility
type Resources struct {
	// Total available resources on the node
	totalResources map[string][]float64

	// Component managers
	reservationRequirements ReservationRequirements
	peakRunningRequirements PeakRunningRequirements

	// Map of task ID -> resource type -> resource info (for API compatibility)
	tasks map[int]map[string]*TaskResourceInfo
}

func NewResources() *Resources {
	return &Resources{
		totalResources:          make(map[string][]float64),
		reservationRequirements: NewReservationRequirements(),
		peakRunningRequirements: NewPeakRunningRequirements(),
		tasks:                   make(map[int]map[string]*TaskResourceInfo),
	}
}

func (nr *Resources) UpdateResources(free map[string][]float64, available map[string][]float64) {
	if free == nil {
		return
	}

	// Use available if provided, otherwise use free
	resources := free
	if available != nil {
		resources = available
	}

	// Update total resources and initialize managers
	for res, values := range resources {
		// Update total resources
		nr.totalResources[res] = copySlice(values)

		// Initialize managers for this resource type
		nr.reservationRequirements.InitializeResource(res, len(values))
		nr.peakRunningRequirements.InitializeResource(res, len(values))

		// Handle node reservation (difference between free and available)
		if available != nil {
			freeVals := free[res]
			nodeReservations := make([]float64, len(values))
			hasNodeReservation := false

			for i, availVal := range values {
				if i < len(freeVals) {
					nodeReservation := freeVals[i] - availVal
					if nodeReservation > 0 {
						nodeReservations[i] = nodeReservation
						hasNodeReservation = true
					}
				}
			}

			if hasNodeReservation {
				nr.reservationRequirements.SetNodeReservation(res, nodeReservations)
			}
		}
	}
}

func (nr *Resources) AddTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	// Remove task first if it exists (update operation)
	nr.RemoveTask(dbNodeTask)

	required := dbNodeTask.OriginTask.RequiredResources
	if required == nil {
		return
	}

	// Initialize task map
	if nr.tasks[taskId] == nil {
		nr.tasks[taskId] = make(map[string]*TaskResourceInfo)
	}

	needsReservation := NeedsReservedResources(dbNodeTask)
	selectedRes := dbNodeTask.OriginTask.SelectedResources
	taskStringId := dbNodeTask.OriginTask.Id

	for res, reqValue := range *required {
		if reqValue == 0 {
			continue
		}

		// Get resource dimensions
		totalSlice := nr.totalResources[res]
		if totalSlice == nil {
			continue
		}
		l := len(totalSlice)

		// Initialize task resource info
		taskInfo := &TaskResourceInfo{
			Reserved: initializeSlice(l),
			Reserve:  initializeSlice(l),
		}

		// Calculate resource requirements
		usage := dbNodeTask.ResourceUsage[res]
		if selectedRes != nil {
			if i, has := selectedRes[res]; has && i < l {
				// FIXED: Calculate reserve amount consistently
				// For reserved tasks, always reserve the full required amount
				// For non-reserved tasks, reserve (required - actual usage)
				var reserveAmount float64

				if needsReservation {
					// For reserved tasks (starting, initializing, recent running),
					// reserve the full required amount to prevent other tasks from using it
					reserveAmount = reqValue
				} else {
					// For non-reserved tasks (old running tasks),
					// reserve only the difference between required and used
					// Distinguish between nil usage (task doesn't declare usage) and 0 usage
					if usage == nil || len(usage) <= i {
						// Task doesn't declare usage for this resource - reserve full required amount
						reserveAmount = reqValue
					} else {
						// Task declares usage (including 0) - reserve difference
						reserveAmount = reqValue - usage[i]
					}
				}

				if reserveAmount > 0 {
					if needsReservation {
						// For reserved tasks, add immediate reservation
						taskInfo.Reserved[i] = reserveAmount
						reservations := initializeSlice(l)
						reservations[i] = reserveAmount
						nr.reservationRequirements.AddReservation(res, taskStringId, reservations)
					} else {
						// For non-reserved tasks, add potential reserve
						taskInfo.Reserve[i] = reserveAmount
						reserves := initializeSlice(l)
						reserves[i] = reserveAmount
						nr.peakRunningRequirements.AddTaskReserve(taskId, res, reserves)
					}
				}
			}
		}

		nr.tasks[taskId][res] = taskInfo
	}
}

func (nr *Resources) RemoveTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	taskResources, exists := nr.tasks[taskId]
	if !exists {
		return
	}

	taskStringId := dbNodeTask.OriginTask.Id

	// Remove task's resource usage from managers
	for res, taskInfo := range taskResources {
		if taskInfo == nil {
			continue
		}

		// Remove reserved resources
		if hasReservations(taskInfo.Reserved) {
			nr.reservationRequirements.RemoveReservation(res, taskStringId, taskInfo.Reserved)
		}

		// Remove potential reserves
		if hasReservations(taskInfo.Reserve) {
			nr.peakRunningRequirements.RemoveTaskReserve(taskId, res)
		}
	}

	delete(nr.tasks, taskId)
}

func (nr *Resources) GetAvailable(resource string) ([]float64, map[string][]float64) {
	totalSlice := nr.totalResources[resource]
	if totalSlice == nil {
		return nil, nil
	}

	// Get reserved and max reserve slices
	totalReserved := nr.reservationRequirements.GetTotalReserved()[resource]
	maxReserve := nr.peakRunningRequirements.GetMaxReserveForResource(resource)

	// Calculate available = total - reserved - maxReserve
	available := make([]float64, len(totalSlice))
	for i := range totalSlice {
		avail := totalSlice[i]

		// Subtract immediate reservations
		if totalReserved != nil && i < len(totalReserved) {
			avail -= totalReserved[i]
		}

		// Subtract maximum reserve
		if maxReserve != nil && i < len(maxReserve) {
			avail -= maxReserve[i]
		}

		if avail < 0 {
			available[i] = 0
		} else {
			available[i] = avail
		}
	}

	// Combine debug information from both managers
	reservationInfo := nr.reservationRequirements.GetReservationInfo(resource)
	peakInfo := nr.peakRunningRequirements.GetReservationInfo(resource)

	// Merge peak running requirements info into reservation info
	if reservationInfo == nil {
		reservationInfo = make(map[string][]float64)
	}
	for key, values := range peakInfo {
		reservationInfo[key] = values
	}

	return available, reservationInfo
}

// GetTaskResourceUsage returns resource usage for a specific task
func (nr *Resources) GetTaskResourceUsage(taskId int) map[string]*TaskResourceInfo {
	return nr.tasks[taskId]
}

// GetTotalReserved returns total reserved resources
func (nr *Resources) GetTotalReserved() map[string][]float64 {
	return nr.reservationRequirements.GetTotalReserved()
}

// GetMaxReserve returns maximum reserves
func (nr *Resources) GetMaxReserve() map[string][]float64 {
	return nr.peakRunningRequirements.GetMaxReserve()
}
