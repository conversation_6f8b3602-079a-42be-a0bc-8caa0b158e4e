# Benchmark Results - Before Implementation (Current Maximum Logic)
# Date: 2025-07-15
# Implementation: Maximum per index logic

goos: linux
goarch: amd64
pkg: git.moderntv.eu/mcloud/system/controller/node
cpu: 13th Gen Intel(R) Core(TM) i7-1355U

BenchmarkPeakRunningRequirements/AddTaskReserve-12         	11129053	       135.0 ns/op	      64 B/op	       1 allocs/op
BenchmarkPeakRunningRequirements/RemoveTaskReserve-12      	   52052	     20844 ns/op	     464 B/op	       3 allocs/op
BenchmarkPeakRunningRequirements/RecalculateMaxReserveForResource-12         	  673214	      1778 ns/op	       0 B/op	       0 allocs/op
BenchmarkPeakRunningRequirements/GetMaxReserveForResource-12                 	177269517	         6.208 ns/op	       0 B/op	       0 allocs/op
BenchmarkPeakRunningRequirements/Complex_scenario_with_many_tasks-12         	    4257	    350590 ns/op	  285421 B/op	    2017 allocs/op
BenchmarkPeakRunningRequirementsMemory/Memory_usage_with_many_tasks-12       	      94	  12533734 ns/op	  465585 B/op	    3000 allocs/op

# Key observations:
# - AddTaskReserve is very fast (135 ns/op) with minimal allocations
# - RemoveTaskReserve is slower (20.8 μs/op) due to recalculation logic
# - RecalculateMaxReserveForResource takes 1.8 μs/op
# - GetMaxReserveForResource is extremely fast (6.2 ns/op) - just returns existing slice
# - Complex scenario with 500 tasks takes 350 μs/op
# - Memory usage test shows 465KB allocated for 1000 tasks
