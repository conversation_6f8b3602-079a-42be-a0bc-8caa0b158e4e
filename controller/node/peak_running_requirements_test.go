package node

import (
	"fmt"
	"testing"

	"github.com/matryer/is"
)

func TestPeakRunningRequirements(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("NewPeakRunningRequirements", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()

		is.True(prr.maxReserve != nil)
		is.True(prr.taskReserves != nil)
	})

	t.Run("InitializeResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()

		// Initialize new resource
		prr.InitializeResource("GPU", 4)

		is.True(prr.maxReserve["GPU"] != nil)
		is.Equal(len(prr.maxReserve["GPU"]), 4)

		// All values should be zero
		for i := 0; i < 4; i++ {
			is.Equal(prr.maxReserve["GPU"][i], 0.0)
		}

		// Initialize existing resource with different size
		prr.InitializeResource("GPU", 6)
		is.Equal(len(prr.maxReserve["GPU"]), 6)
	})

	t.Run("AddTaskReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 4)

		// Add first task reserve
		reserves1 := []float64{10.0, 0.0, 20.0, 0.0}
		prr.AddTaskReserve(1, "GPU", reserves1)

		// Check max reserve (should match first task)
		maxReserve := prr.maxReserve["GPU"]
		is.Equal(maxReserve[0], 10.0)
		is.Equal(maxReserve[1], 0.0)
		is.Equal(maxReserve[2], 20.0)
		is.Equal(maxReserve[3], 0.0)

		// Check task reserves
		taskReserve := prr.taskReserves[1]["GPU"]
		is.True(taskReserve != nil)
		is.Equal(len(taskReserve), 4)
		is.Equal(taskReserve[0], 10.0)
		is.Equal(taskReserve[2], 20.0)

		// Add second task with higher reserves
		reserves2 := []float64{5.0, 25.0, 15.0, 30.0}
		prr.AddTaskReserve(2, "GPU", reserves2)

		// Check max reserve (should be from task with largest total difference)
		// Task1 total: 10+0+20+0 = 30
		// Task2 total: 5+25+15+30 = 75 (largest)
		maxReserve = prr.maxReserve["GPU"]
		is.Equal(maxReserve[0], 5.0)  // Task2 has largest total difference
		is.Equal(maxReserve[1], 25.0) // Task2 has largest total difference
		is.Equal(maxReserve[2], 15.0) // Task2 has largest total difference
		is.Equal(maxReserve[3], 30.0) // Task2 has largest total difference
	})

	t.Run("AddTaskReserve edge cases", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Empty reserves should be ignored
		prr.AddTaskReserve(1, "GPU", []float64{})
		is.Equal(prr.maxReserve["GPU"][0], 0.0)
		is.Equal(prr.maxReserve["GPU"][1], 0.0)

		// Nil reserves should be ignored
		prr.AddTaskReserve(2, "GPU", nil)
		is.Equal(prr.maxReserve["GPU"][0], 0.0)
		is.Equal(prr.maxReserve["GPU"][1], 0.0)

		// Multiple resources for same task
		prr.AddTaskReserve(3, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(3, "CPU", []float64{5.0, 15.0})

		is.True(prr.taskReserves[3]["GPU"] != nil)
		is.True(prr.taskReserves[3]["CPU"] != nil)
	})

	t.Run("RemoveTaskReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 3)

		// Add multiple tasks
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})
		prr.AddTaskReserve(3, "GPU", []float64{15.0, 10.0, 20.0})

		// Max should be from task with largest total difference
		// Task1 total: 10+20+30 = 60 (largest)
		// Task2 total: 5+25+15 = 45
		// Task3 total: 15+10+20 = 45
		maxReserve := prr.maxReserve["GPU"]
		is.Equal(maxReserve[0], 10.0) // Task1 has largest total difference
		is.Equal(maxReserve[1], 20.0) // Task1 has largest total difference
		is.Equal(maxReserve[2], 30.0) // Task1 has largest total difference

		// Remove task 2 (not the one with largest difference)
		prr.RemoveTaskReserve(2, "GPU")

		// Task should be removed
		is.Equal(prr.taskReserves[2], map[string][]float64(nil))

		// Max should remain the same since Task1 still has largest difference
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 10.0) // Task1 still has largest total difference
		is.Equal(maxReserve[1], 20.0) // Task1 still has largest total difference
		is.Equal(maxReserve[2], 30.0) // Task1 still has largest total difference

		// Remove task 1 (the one with largest difference)
		prr.RemoveTaskReserve(1, "GPU")

		// Now task 3 should be the one with largest difference
		// Task3 total: 15+10+20 = 45
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 15.0) // Task3 now has largest total difference
		is.Equal(maxReserve[1], 10.0) // Task3 now has largest total difference
		is.Equal(maxReserve[2], 20.0) // Task3 now has largest total difference
	})

	t.Run("RemoveTaskReserve edge cases", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Remove non-existent task should not crash
		prr.RemoveTaskReserve(999, "GPU")
		// No task should be present
		is.Equal(prr.taskReserves[999], map[string][]float64(nil))

		// Remove non-existent resource from existing task should not crash
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.RemoveTaskReserve(1, "CPU")
		// Task 1 should still have GPU resource
		is.True(prr.taskReserves[1]["GPU"] != nil)
		is.Equal(prr.taskReserves[1]["CPU"], []float64(nil))
	})

	t.Run("RecalculateMaxReserveForResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 3)

		// Add tasks
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})
		prr.AddTaskReserve(3, "GPU", []float64{15.0, 10.0, 20.0})

		// Manually corrupt max reserve
		prr.maxReserve["GPU"][0] = 999.0
		prr.maxReserve["GPU"][1] = 888.0
		prr.maxReserve["GPU"][2] = 777.0

		// Recalculate
		prr.RecalculateMaxReserveForResource("GPU")

		// Should be correct again - task with largest total difference
		// Task1 total: 10+20+30 = 60 (largest)
		// Task2 total: 5+25+15 = 45
		// Task3 total: 15+10+20 = 45
		maxReserve := prr.maxReserve["GPU"]
		is.Equal(maxReserve[0], 10.0) // Task1 has largest total difference
		is.Equal(maxReserve[1], 20.0) // Task1 has largest total difference
		is.Equal(maxReserve[2], 30.0) // Task1 has largest total difference

		// Recalculate non-existent resource
		prr.RecalculateMaxReserveForResource("NONEXISTENT")
		// Should not crash
	})

	t.Run("GetMaxReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)
		prr.InitializeResource("CPU", 3)

		// Add some reserves
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(1, "CPU", []float64{5.0, 15.0, 25.0})

		result := prr.GetMaxReserve()

		// Values should match
		is.Equal(len(result["GPU"]), 2)
		is.Equal(result["GPU"][0], 10.0)
		is.Equal(result["GPU"][1], 20.0)

		is.Equal(len(result["CPU"]), 3)
		is.Equal(result["CPU"][0], 5.0)
		is.Equal(result["CPU"][1], 15.0)
		is.Equal(result["CPU"][2], 25.0)

		// Modifying result should not affect original
		result["GPU"][0] = 999.0
		is.Equal(prr.maxReserve["GPU"][0], 10.0)
	})

	t.Run("GetMaxReserveForResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 3)

		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})

		result := prr.GetMaxReserveForResource("GPU")

		// Should return reserves from task with largest total difference
		// Task1 total: 10+20+30 = 60 (largest)
		// Task2 total: 5+25+15 = 45
		is.Equal(len(result), 3)
		is.Equal(result[0], 10.0) // Task1 has largest total difference
		is.Equal(result[1], 20.0) // Task1 has largest total difference
		is.Equal(result[2], 30.0) // Task1 has largest total difference

		// Non-existent resource
		nilResult := prr.GetMaxReserveForResource("NONEXISTENT")
		is.Equal(nilResult, []float64(nil))
	})

	t.Run("Complex scenario", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Add multiple tasks
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(2, "GPU", []float64{15.0, 5.0})
		prr.AddTaskReserve(3, "GPU", []float64{8.0, 25.0})

		// Max should be from task with largest total difference
		// Task1 total: 10+20 = 30 (largest)
		// Task2 total: 15+5 = 20
		// Task3 total: 8+25 = 33 (largest)
		maxReserve := prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 8.0)  // Task3 has largest total difference
		is.Equal(maxReserve[1], 25.0) // Task3 has largest total difference

		// Remove task 2 (not the one with largest difference)
		prr.RemoveTaskReserve(2, "GPU")

		// Max should remain the same since Task3 still has largest difference
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 8.0)  // Task3 still has largest total difference
		is.Equal(maxReserve[1], 25.0) // Task3 still has largest total difference

		// Remove task 3 (the one with largest difference)
		prr.RemoveTaskReserve(3, "GPU")

		// Now task 1 should be the one with largest difference
		// Task1 total: 10+20 = 30
		maxReserve = prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 10.0) // Task1 now has largest total difference
		is.Equal(maxReserve[1], 20.0) // Task1 now has largest total difference
	})

	t.Run("GetReservationInfo", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Add task reserves
		prr.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prr.AddTaskReserve(2, "GPU", []float64{5.0, 25.0})

		// Get reservation info
		info := prr.GetReservationInfo("GPU")
		is.True(info != nil)

		// Should have (backup) entry with reserves from task with largest difference
		// Task1 total: 10+20 = 30
		// Task2 total: 5+25 = 30 (both have same total, implementation should pick one)
		backup := info["(backup)"]
		is.True(backup != nil)
		is.Equal(len(backup), 2)

		// Should also have task_id entry
		taskEntry := info[fmt.Sprintf("(task_%d)", prr.maxReserveTaskId["GPU"])]
		is.True(taskEntry != nil)
		is.Equal(len(taskEntry), 2)

		// Test with non-existent resource
		info = prr.GetReservationInfo("CPU")
		is.True(info != nil)
		is.Equal(len(info), 0) // Should be empty
	})

	// NEW TESTS FOR SINGLE TASK RESERVE LOGIC
	t.Run("single task reserve logic - basic", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 1)

		// Add tasks with different reserves
		// Task1: reserve 400 (required=500, usage=100)
		// Task2: reserve 250 (required=300, usage=50)
		// Task3: reserve 200 (required=800, usage=600)
		// Largest difference should be Task1 (400), not Task3 with highest required
		prr.AddTaskReserve(1, "GPU", []float64{400.0})
		prr.AddTaskReserve(2, "GPU", []float64{250.0})
		prr.AddTaskReserve(3, "GPU", []float64{200.0})

		// Current implementation uses maximum (400), but new logic should use
		// largest difference from single task (also 400 in this case)
		maxReserve := prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 400.0) // Should be largest difference from single task
	})

	t.Run("single task reserve logic - different from maximum", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Create scenario where largest difference is NOT the maximum
		// Task1: reserves [100, 300] - total difference = 400
		// Task2: reserves [200, 150] - total difference = 350
		// Task3: reserves [150, 400] - total difference = 550 (largest)
		// Old logic: max([100,200,150], [300,150,400]) = [200, 400]
		// New logic: should use Task3 entirely = [150, 400]
		prr.AddTaskReserve(1, "GPU", []float64{100.0, 300.0})
		prr.AddTaskReserve(2, "GPU", []float64{200.0, 150.0})
		prr.AddTaskReserve(3, "GPU", []float64{150.0, 400.0})

		// New implementation gives [150, 400] (entire task with largest total difference)
		maxReserve := prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 150.0) // Task3 has largest total difference (550)
		is.Equal(maxReserve[1], 400.0) // Task3 has largest total difference (550)
	})

	t.Run("single task reserve logic - nil vs zero distinction", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 2)

		// Test scenario for nil vs 0 distinction
		// Task1: has reserves for both indices
		// Task2: has reserves only for index 0 (index 1 should be treated as nil/missing)
		prr.AddTaskReserve(1, "GPU", []float64{100.0, 200.0})
		prr.AddTaskReserve(2, "GPU", []float64{350.0}) // Only index 0, index 1 is missing

		// Task1 total: 100+200 = 300
		// Task2 total: 350+0 = 350 (largest, missing index treated as 0)
		maxReserve := prr.GetMaxReserveForResource("GPU")
		is.Equal(maxReserve[0], 350.0) // Task2 has largest total difference
		is.Equal(maxReserve[1], 0.0)   // Task2 doesn't have index 1
	})
}
