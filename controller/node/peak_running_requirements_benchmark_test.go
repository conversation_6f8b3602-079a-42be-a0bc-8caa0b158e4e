package node

import (
	"testing"
)

// BenchmarkPeakRunningRequirements benchmarks the current implementation
func BenchmarkPeakRunningRequirements(t *testing.B) {
	t.Run("AddTaskReserve", func(b *testing.B) {
		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 8)

		reserves := []float64{100.0, 200.0, 150.0, 300.0, 50.0, 250.0, 180.0, 120.0}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			taskID := i % 1000 // Cycle through 1000 different task IDs
			prr.AddTaskReserve(taskID, "GPU", reserves)
		}
	})

	t.Run("RemoveTaskReserve", func(b *testing.B) {
		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 8)

		reserves := []float64{100.0, 200.0, 150.0, 300.0, 50.0, 250.0, 180.0, 120.0}

		// Pre-populate with tasks
		for i := 0; i < 1000; i++ {
			prr.AddTaskReserve(i, "GPU", reserves)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			taskID := i % 1000
			prr.RemoveTaskReserve(taskID, "GPU")
			// Re-add to maintain state
			prr.AddTaskReserve(taskID, "GPU", reserves)
		}
	})

	t.Run("RecalculateMaxReserveForResource", func(b *testing.B) {
		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 8)

		reserves := []float64{100.0, 200.0, 150.0, 300.0, 50.0, 250.0, 180.0, 120.0}

		// Pre-populate with tasks
		for i := 0; i < 100; i++ {
			prr.AddTaskReserve(i, "GPU", reserves)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			prr.RecalculateMaxReserveForResource("GPU")
		}
	})

	t.Run("GetMaxReserveForResource", func(b *testing.B) {
		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 8)

		reserves := []float64{100.0, 200.0, 150.0, 300.0, 50.0, 250.0, 180.0, 120.0}

		// Pre-populate with tasks
		for i := 0; i < 100; i++ {
			prr.AddTaskReserve(i, "GPU", reserves)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = prr.GetMaxReserveForResource("GPU")
		}
	})

	t.Run("Complex scenario with many tasks", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			prr := NewPeakRunningRequirements()
			prr.InitializeResource("GPU", 8)
			prr.InitializeResource("CPU", 4)

			// Add many tasks with different reserves
			for taskID := 0; taskID < 500; taskID++ {
				gpuReserves := []float64{
					float64(taskID%100 + 50),
					float64(taskID%150 + 100),
					float64(taskID%200 + 75),
					float64(taskID%120 + 200),
					float64(taskID%80 + 30),
					float64(taskID%180 + 150),
					float64(taskID%90 + 80),
					float64(taskID%160 + 120),
				}
				cpuReserves := []float64{
					float64(taskID%300 + 100),
					float64(taskID%250 + 200),
					float64(taskID%180 + 150),
					float64(taskID%220 + 180),
				}

				prr.AddTaskReserve(taskID, "GPU", gpuReserves)
				prr.AddTaskReserve(taskID, "CPU", cpuReserves)
			}

			// Remove some tasks (triggers recalculation)
			for taskID := 0; taskID < 100; taskID += 5 {
				prr.RemoveTaskReserve(taskID, "GPU")
				prr.RemoveTaskReserve(taskID, "CPU")
			}

			// Get final reserves
			_ = prr.GetMaxReserveForResource("GPU")
			_ = prr.GetMaxReserveForResource("CPU")
		}
	})
}

// BenchmarkPeakRunningRequirementsMemory benchmarks memory usage
func BenchmarkPeakRunningRequirementsMemory(b *testing.B) {
	b.Run("Memory usage with many tasks", func(b *testing.B) {
		prr := NewPeakRunningRequirements()
		prr.InitializeResource("GPU", 8)

		reserves := []float64{100.0, 200.0, 150.0, 300.0, 50.0, 250.0, 180.0, 120.0}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Add tasks
			for taskID := 0; taskID < 1000; taskID++ {
				prr.AddTaskReserve(taskID, "GPU", reserves)
			}

			// Remove all tasks
			for taskID := 0; taskID < 1000; taskID++ {
				prr.RemoveTaskReserve(taskID, "GPU")
			}
		}
	})
}
