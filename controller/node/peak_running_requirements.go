package node

// PeakRunningRequirements holds the largest difference from a single task for running tasks.
// These reserves are calculated as the largest total difference between required resources
// and actually used resources from a single running task (those with NeedsReservedResource false).
type PeakRunningRequirements struct {
	maxReserve       map[string][]float64         // largest difference from single task
	maxReserveTaskId map[string]int               // ID of task with largest difference
	taskReserves     map[int]map[string][]float64 // taskID -> resourceType -> reserves
}

// NewPeakRunningRequirements creates a new peak running requirements tracker
func NewPeakRunningRequirements() PeakRunningRequirements {
	return PeakRunningRequirements{
		maxReserve:       make(map[string][]float64),
		maxReserveTaskId: make(map[string]int),
		taskReserves:     make(map[int]map[string][]float64),
	}
}

// InitializeResource initializes max reserve tracking for a resource type
func (prr *PeakRunningRequirements) InitializeResource(resourceType string, size int) {
	if prr.maxReserve[resourceType] == nil {
		prr.maxReserve[resourceType] = initializeSlice(size)
	} else {
		slice := prr.maxReserve[resourceType]
		ensureSliceSize(&slice, size)
		prr.maxReserve[resourceType] = slice
	}
}

// findLargestDifference finds the task with the largest total difference for a resource
func (prr *PeakRunningRequirements) findLargestDifference(resourceType string) (int, []float64) {
	var largestDiffTaskId int
	var largestDiff []float64
	var maxTotalDiff float64

	// Find task with largest total difference
	for taskId, taskReserves := range prr.taskReserves {
		if reserves := taskReserves[resourceType]; reserves != nil {
			// Calculate total difference for this task
			totalDiff := 0.0
			for _, diff := range reserves {
				totalDiff += diff
			}

			// If this task has larger total difference, use it
			if totalDiff > maxTotalDiff {
				maxTotalDiff = totalDiff
				largestDiffTaskId = taskId
				largestDiff = copySlice(reserves)
			}
		}
	}

	return largestDiffTaskId, largestDiff
}

// AddTaskReserve adds potential reserves for a task and updates max reserves
func (prr *PeakRunningRequirements) AddTaskReserve(taskID int, resourceType string, reserves []float64) {
	if len(reserves) == 0 {
		return
	}

	// Initialize task reserves if needed
	if prr.taskReserves[taskID] == nil {
		prr.taskReserves[taskID] = make(map[string][]float64)
	}

	// Store task reserves
	prr.taskReserves[taskID][resourceType] = copySlice(reserves)

	// Find task with largest difference and update max reserve
	taskId, largestDiff := prr.findLargestDifference(resourceType)

	// Update max reserve and task ID
	if largestDiff != nil {
		prr.maxReserve[resourceType] = largestDiff
		prr.maxReserveTaskId[resourceType] = taskId
	}
}

// RemoveTaskReserve removes potential reserves for a task
func (prr *PeakRunningRequirements) RemoveTaskReserve(taskID int, resourceType string) {
	if prr.taskReserves[taskID] == nil {
		return
	}

	reserves := prr.taskReserves[taskID][resourceType]
	if reserves == nil {
		return
	}

	// Check if this task had the largest difference - if so, we need to recalculate
	needsRecalc := false
	if prr.maxReserveTaskId[resourceType] == taskID {
		needsRecalc = true
	}

	// Remove task reserves
	delete(prr.taskReserves[taskID], resourceType)
	if len(prr.taskReserves[taskID]) == 0 {
		delete(prr.taskReserves, taskID)
	}

	// Recalculate if needed
	if needsRecalc {
		taskId, largestDiff := prr.findLargestDifference(resourceType)
		if largestDiff != nil {
			prr.maxReserve[resourceType] = largestDiff
			prr.maxReserveTaskId[resourceType] = taskId
		} else {
			// No tasks left, reset to zero
			if prr.maxReserve[resourceType] != nil {
				for i := range prr.maxReserve[resourceType] {
					prr.maxReserve[resourceType][i] = 0
				}
			}
			prr.maxReserveTaskId[resourceType] = 0
		}
	}
}

// RecalculateMaxReserveForResource recalculates max reserve for a specific resource
func (prr *PeakRunningRequirements) RecalculateMaxReserveForResource(resourceType string) {
	taskId, largestDiff := prr.findLargestDifference(resourceType)

	if largestDiff != nil {
		prr.maxReserve[resourceType] = largestDiff
		prr.maxReserveTaskId[resourceType] = taskId
	} else {
		// No tasks left, reset to zero
		if prr.maxReserve[resourceType] != nil {
			for i := range prr.maxReserve[resourceType] {
				prr.maxReserve[resourceType][i] = 0
			}
		}
		prr.maxReserveTaskId[resourceType] = 0
	}
}

// GetMaxReserve returns a copy of max reserves
func (prr *PeakRunningRequirements) GetMaxReserve() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range prr.maxReserve {
		result[res] = copySlice(values)
	}
	return result
}

// GetMaxReserveForResource returns max reserve for a specific resource
func (prr *PeakRunningRequirements) GetMaxReserveForResource(resourceType string) []float64 {
	return prr.maxReserve[resourceType]
}

// GetReservationInfo returns debug information about maximum reserves for a specific resource
func (prr *PeakRunningRequirements) GetReservationInfo(resourceType string) map[string][]float64 {
	info := make(map[string][]float64)
	if maxReserve := prr.maxReserve[resourceType]; maxReserve != nil {
		info["(backup)"] = copySlice(maxReserve)
	}
	return info
}
